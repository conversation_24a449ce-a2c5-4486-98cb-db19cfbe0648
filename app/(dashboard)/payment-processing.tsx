import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { apiRequest } from '@/utils/apiUtils';
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withTiming, withSequence, Easing } from 'react-native-reanimated';
import { Svg, Path } from 'react-native-svg';
import type { PaymentStatus } from '@/types';
import { formatCurrency } from '@/utils/i10n';

export default function PaymentProcessingScreen() {
  const { paymentId, amount } = useLocalSearchParams<{ paymentId: string, amount: string }>();

  const [status, setStatus] = useState<string>('processing');
  const [error, setError] = useState<string | null>(null);
  const [attempts, setAttempts] = useState(0);
  const [paymentDetails, setPaymentDetails] = useState<PaymentStatus | null>(null);
  const timeoutRef = useRef<any>(null);
  const MAX_ATTEMPTS = 60; // e.g., 120 attempts * 1 second = 2 minutes timeout

  // Animation value for progress bar
  const translateX = useSharedValue(-100);

  // Create animated style for progress indicator
  const animatedProgressStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  // Start progress bar animation
  useEffect(() => {
    translateX.value = withRepeat(
      withSequence(
        withTiming(400, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        withTiming(-100, { duration: 0 })
      ),
      -1, // Infinite repetitions
      false // Don't reverse
    );
  }, []);

  // Check if we need to update status based on time passed
  useEffect(() => {
    // Set up a timer to check if we should update to "taking-longer" after 10 seconds
    const takingLongerTimer = setTimeout(() => {
      if (status === 'processing') {
        setStatus('taking-longer');
      }
    }, 10000); // 10 seconds

    return () => clearTimeout(takingLongerTimer);
  }, [status]);

  // Effect to start and manage polling
  useEffect(() => {
    // **IMPORTANT: If status is no longer processing, stop polling**
    console.log(status)
    if (status !== 'processing') {
      // Ensure any lingering timeout is cleared
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      return; // Exit the effect
    }

    // If we've exceeded max attempts, set timeout status
    if (attempts >= MAX_ATTEMPTS) {
      console.log('⏰ Max attempts reached, setting status to timeout.');
      setStatus('timeout');
      // Ensure any lingering timeout is cleared
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      return; // Exit the effect
    }

    // Schedule the next check if still processing and within attempt limits
    console.log(`🔄 Scheduling check attempt ${attempts + 1}/${MAX_ATTEMPTS} for status: ${status}`);
    timeoutRef.current = setTimeout(() => {
      console.log(`🚀 Running check attempt ${attempts + 1}`);
      checkPaymentStatus(timeoutRef); // Pass the ref here
    }, 1000); // Poll every 1 second

    // Clean up the timeout when the effect re-runs or component unmounts
    return () => {
      console.log('🧹 Clearing timeout ref in cleanup');
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  // **Run this effect when paymentId, attempts, or status changes**
  }, [paymentId, attempts, status]);

  // Initial check when component mounts (if needed, but checkPaymentStatus handles attempts)
  useEffect(() => {
    console.log('🚀 Initial component mount: Kicking off first status check.');
    checkPaymentStatus(timeoutRef); // Start the first check
  }, [paymentId]); // Only run on mount based on paymentId

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Processing Payment - CasaPay';
    }
  }, []);

  const checkPaymentStatus = async (currentTimeoutRef: React.MutableRefObject<any>) => {
    try {
      // Mock payment status check for demo purposes
      console.log(`🎭 Mock payment status check attempt ${attempts + 1}`);

      // Simulate successful payment after 3-5 attempts (3-5 seconds)
      if (attempts >= 3) {
        console.log('✅ Mock payment completed successfully');

        const mockPaymentDetails: PaymentStatus = {
          payment_id: paymentId || 'mock-payment',
          status: 'completed',
          amount: amount || '0',
          currency: 'GBP',
          payment_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          completed: true
        };

        setPaymentDetails(mockPaymentDetails);
        setStatus('completed');

        // Clear the timeout immediately
        if (currentTimeoutRef.current) {
          clearTimeout(currentTimeoutRef.current);
        }

        return;
      }

      // Still processing - increment attempts
      setAttempts(prev => prev + 1);

      // Original API call (commented out for mock)
      /*
      const response = await apiRequest(
        '/api/v1/tenant/mcp',
        'POST',
        {
          jsonrpc: '2.0',
          method: 'tenant.check_payment_status',
          params: {
            payment_id: paymentId
          }
        },
      );

      console.log('📊 Payment status response:', JSON.stringify(response?.result, null, 2));

      if (response && response.result) {
        setPaymentDetails(response.result);
        if (response.result.completed || response.result.status === 'completed') {
          // Payment is complete, set success status
          setStatus('completed');
          // Clear the timeout immediately
          if (currentTimeoutRef.current) {
            clearTimeout(currentTimeoutRef.current);
          }

          // No need to navigate to success screen anymore since we show success state here
        } else if (response.result.status === 'failed' || response.result.status === 'error') {
          // Payment failed
          setStatus('failed');
          setError('Payment processing failed. Please try again.');
        } else {
          // Payment still processing, increment attempts and continue polling
          setAttempts(prev => prev + 1);
        }
      } else {
        throw new Error('Invalid response format');
      }
      */
    } catch (err) {
      console.error('Error checking payment status:', err);

      // Only set error after multiple failed attempts
      if (attempts > 3) {
        setError('Failed to check payment status. Please check your dashboard later.');
        setStatus('error');
      } else {
        // Wait 1 second before incrementing attempts to avoid rapid retries on error
        setAttempts(prev => prev + 1);
      }
    }
  };

  // Handle closing the screen
  const handleClose = () => {
    router.replace('/(dashboard)');
  };

  // Render different content based on status
  const renderContent = () => {
    if (error) {
      return (
        <View className="items-center">
          <Text className="text-red-600 text-lg font-bold mb-2">Error</Text>
          <Text className="text-gray-600 text-center mb-4">{error}</Text>
          <View className="bg-red-100 p-4 rounded-lg w-full">
            <Text className="text-red-800">
              Your payment may still be processing. Please check your dashboard later for confirmation.
            </Text>
          </View>
        </View>
      );
    }

    switch (status) {
      case 'processing':
        return (
          <View className="items-center">
            <Text className="text-xl font-bold mb-4">Processing Payment</Text>
            <Text className="text-gray-600 text-center mb-6">
              Please don't close this window.
              This will take about 10 seconds.
            </Text>
          </View>
        );

      case 'taking-longer':
        return (
          <View className="items-center">
            <Svg width="49" height="48" viewBox="0 0 49 48" fill="none" style={{ marginBottom: 16 }}>
              <Path d="M24.25 4C13.21 4 4.25 12.96 4.25 24C4.25 35.04 13.21 44 24.25 44C35.29 44 44.25 35.04 44.25 24C44.25 12.96 35.29 4 24.25 4ZM26.25 34H22.25V30H26.25V34ZM26.25 26H22.25V14H26.25V26Z" fill="#F59E0B"/>
            </Svg>
            <Text className="text-xl font-bold mb-4">Taking longer than expected</Text>
            <Text className="text-gray-600 text-center mb-6">
              Don't worry, we're processing your payment in the background.
              You can safely close this window.
            </Text>
            <TouchableOpacity
              className="bg-secondary rounded-xl py-[10px] px-4 items-center mb-4 w-[320px]"
              onPress={handleClose}
            >
              <Text className="text-white font-medium">Close</Text>
            </TouchableOpacity>
          </View>
        );

      case 'completed':
        return (
          <View className="items-center">
            <Svg width="49" height="48" viewBox="0 0 49 48" fill="none" style={{ marginBottom: 16 }}>
              <Path d="M24.7461 4C13.7061 4 4.74609 12.96 4.74609 24C4.74609 35.04 13.7061 44 24.7461 44C35.7861 44 44.7461 35.04 44.7461 24C44.7461 12.96 35.7861 4 24.7461 4ZM20.3461 34L10.3461 24L13.5861 20.76L20.3461 27.52L35.9061 12L39.1461 15.24L20.3461 34Z" fill="#059669"/>
            </Svg>
            <Text className="text-xl text-center font-bold mb-4">
              Your payment has been processed successfully.
              You can now close this window.
            </Text>
            <TouchableOpacity
              className="bg-secondary rounded-xl py-[10px] px-4 items-center mb-4 w-[320px]"
              onPress={handleClose}
            >
              <Text className="text-white font-medium">Close</Text>
            </TouchableOpacity>
          </View>
        );

      case 'timeout':
        return (
          <View className="items-center">
            <Text className="text-xl font-bold mb-4">Payment In Progress</Text>
            <Text className="text-gray-600 text-center mb-6">
              Your payment is still being processed by our system. You can safely return to the dashboard and check back later.
            </Text>
          </View>
        );

      default:
        return (
          <View className="items-center">
            <Text className="text-xl font-bold mb-4">Processing Payment</Text>
            <Text className="text-gray-600 text-center mb-6">
              Please don't close this window.
              This will take about 10 seconds.
            </Text>
          </View>
        );
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />
      <View className="flex-1 justify-center max-w-[640px] mx-auto w-full items-center p-6">
        {/* Status content */}
        {renderContent()}

        {/* Progress bar - only show for processing state */}
        {(status === 'processing' || status === 'timeout') && (
          <View style={styles.progressBarContainer}>
            <Animated.View style={[styles.progressIndicator, animatedProgressStyle]} />
          </View>
        )}

        {/* Payment details if available */}
        {paymentDetails && (
          <View className="mt-8 bg-gray-50 p-4 rounded-lg w-full">
            <Text className="text-gray-800 font-medium mb-2">Payment Details</Text>
            <View className="flex-row justify-between mb-1">
              <Text className="text-gray-600">Amount:</Text>
              <Text className="font-medium">{formatCurrency(parseFloat(paymentDetails.amount), paymentDetails.currency)}</Text>
            </View>
            <View className="flex-row justify-between mb-1">
              <Text className="text-gray-600">Status:</Text>
              <Text className="font-medium">{paymentDetails.status}</Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Payment ID:</Text>
              <Text className="font-medium">{paymentDetails.payment_id}</Text>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

// Styles
const styles = StyleSheet.create({
  progressBarContainer: {
    width: 400,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 100,
    overflow: 'hidden',
    marginTop: 8,
    marginBottom: 24,
  },
  progressIndicator: {
    width: 100,
    height: 4,
    backgroundColor: '#3B82F6',
    borderRadius: 100,
  }
});
