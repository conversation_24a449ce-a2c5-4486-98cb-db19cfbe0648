import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons, Feather } from '@expo/vector-icons';
import { apiRequest } from '@/utils/apiUtils';
import { PaymentAgreement } from '@/types';
import { TENANT_PLANS } from '@/utils/constants';
import { formatCurrency } from '@/utils/i10n';

export default function PaymentAgreementConfirmScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [agreement, setAgreement] = useState<PaymentAgreement | null>(null);
  const [paymentLines, setPaymentLines] = useState<any[]>([]);

  useEffect(() => {
    fetchAgreementDetails();
  }, [id]);

  const fetchAgreementDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch agreement details using apiRequest
      const agreementResult = await apiRequest('/api/v1/tenant/mcp', 'POST', {
        jsonrpc: '2.0',
        method: 'tenant.get_payment_agreement',
        params: {
          agreement_id: parseInt(id as string)
        }
      });

      if (agreementResult.error) {
        throw new Error(agreementResult.error.message || 'Failed to fetch agreement details');
      }

      setAgreement(agreementResult.result.payment_agreement);

      // Fetch outstanding payments
      const outstandingPaymentsResult = await apiRequest(
        '/api/v1/tenant/mcp',
        'POST',
        {
          jsonrpc: '2.0',
          method: 'tenant.get_outstanding_payments',
          params: {
            agreement_id: parseInt(id as string)
          }
        },
      );

      if (outstandingPaymentsResult.error) {
        throw new Error(outstandingPaymentsResult.error.message || 'Failed to fetch outstanding payments');
      }

      if (outstandingPaymentsResult.result && outstandingPaymentsResult.result.outstanding_payments) {
        const payments = outstandingPaymentsResult.result.outstanding_payments;
        // Extract payment lines from the first payment
        if (payments.length > 0 && payments[0].lines) {
          setPaymentLines(payments[0].lines);
        }
      }

      setLoading(false);

      if (typeof document !== 'undefined') {
        document.title = `${agreementResult.result.payment_agreement.property?.street_address} - CasaPay`;
      }
    } catch (err) {
      console.error('Error fetching agreement details:', err);
      setError('Failed to load agreement details. Please try again.');
      setLoading(false);
    }
  };

  const handleConfirm = async () => {
    try {
      // Call API to confirm the payment agreement
      const confirmResult = await apiRequest('/api/v1/tenant/mcp', 'POST', {
        jsonrpc: '2.0',
        method: 'tenant.confirm_payment_agreement',
        params: {
          agreement_id: parseInt(id as string)
        }
      });

      if (confirmResult.error) {
        throw new Error(confirmResult.error.message || 'Failed to confirm agreement');
      }

      // After successful confirmation, redirect to payment page
      router.push(`/(dashboard)/payment-agreement/${id}/payment`);
    } catch (error) {
      console.error('Error confirming agreement:', error);
      // Show error alert or message to the user
      alert('Failed to confirm agreement. Please try again.');
    }
  };

  const handleBack = () => {
    router.push(`/(dashboard)`);
  };


  const firstPaymentTotal = paymentLines.reduce((sum, line) => sum + parseFloat(line.total_price || 0), 0);

  const depositAmount = 0;
  const totalDue = firstPaymentTotal + depositAmount;

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 items-center justify-center">
        <ActivityIndicator size="large" color="#4ca2f5" />
        <Text className="mt-4 text-gray-600">Loading agreement details...</Text>
      </SafeAreaView>
    );
  }

  if (error || !agreement) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 items-center justify-center">
        <Feather name="alert-circle" size={48} color="#EF4444" />
        <Text className="mt-4 text-gray-800 font-medium">{error || 'Agreement not found'}</Text>
        <TouchableOpacity
          className="mt-6 bg-[#006494] py-3 px-6 rounded-lg"
          onPress={() => router.push('/(dashboard)')}
        >
          <Text className="text-white font-medium">Go to Dashboard</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar style="dark" />
      <ScrollView className="flex-1 p-6">
        <View className='bg-white max-w-[640px] mx-auto w-full rounded-lg p-5'>


        {/* Header with back button */}
        <View className="flex-row items-center mb-6">
          <TouchableOpacity onPress={handleBack} className="mr-4">
            <Ionicons name="arrow-back" size={24} color="#000" />
          </TouchableOpacity>
          <Text className="text-2xl font-semibold">Payment Agreement Summary</Text>
        </View>

        {/* Operator Information */}
        <View className="flex-row items-center mb-6 bg-white p-4">
          <View className="w-12 h-12 bg-blue-100 rounded-full mr-4 items-center justify-center">
            <Feather name="home" size={24} color="#3B82F6" />
          </View>
          <View>
            <Text className="font-medium">{agreement.operator?.name || '-'}</Text>
            <Text className="text-gray-500">
              {agreement.operator?.email || '-'} · {agreement.operator?.phone || '-'}
            </Text>
          </View>
        </View>

        {/* Property and Plan */}
        <View className="grid md:grid-cols-2 mb-2">
          <View>
            <Text className="text-gray-500 mb-1">Property</Text>
            <Text className="font-medium mb-4">{agreement.property?.street_address}, {agreement.property?.postal_code} {agreement.property?.city}</Text>
          </View>
          <View>
            <Text className="text-gray-500 mb-1">Selected plan</Text>
            <Text className="font-medium">{TENANT_PLANS[agreement.tenant_plan as keyof typeof TENANT_PLANS] || agreement.tenant_plan || '-'}</Text>
          </View>
        </View>

        {/* Agreement Duration */}
        <View className="mb-6">
          <Text className="text-gray-500 mb-1">Agreement duration</Text>
          <Text className="font-medium">
            {agreement.agreement_start ? new Date(agreement.agreement_start).toLocaleDateString() : 'N/A'} - {agreement.agreement_end ? new Date(agreement.agreement_end).toLocaleDateString() : 'N/A'}
          </Text>
        </View>

        {/* Monthly Rent (Base) */}
        <View className="flex-row justify-between items-start mb-4">
          <View>
            <Text className="font-medium">Monthly rent</Text>
            <Text className="text-gray-500">
              {agreement.utilities_included ? 'Including utilities' : 'Excluding utilities'}
            </Text>
          </View>
          <Text className="font-medium">{formatCurrency(parseFloat(agreement.base_monthly_rent), agreement.currency)}</Text>
        </View>

        {/* Service Fee (Base) */}
        <View className="flex-row justify-between items-start mb-6">
          <View>
            <Text className="font-medium">Service fee</Text>
            <Text className="text-gray-500">Monthly billing</Text>
          </View>
          <Text className="font-medium">{formatCurrency(parseFloat(agreement.base_monthly_fee_amount), agreement.currency)}</Text>
        </View>

        {/* First Payment Details from API Lines */}
        <View className="mb-6">
          <Text className="font-medium text-base mb-4">First Payment Details</Text>

          <View className="bg-gray-50 p-4 rounded-lg">
            {/* Dynamically display payment lines */}
            {paymentLines.map((line, index) => (
              <View key={index} className={`flex-row justify-between py-3 ${index > 0 ? 'border-t border-gray-200' : ''}`}>
                <Text className="text-gray-700">{line.description}</Text>
                <Text className="font-medium">{formatCurrency(parseFloat(line.total_price), agreement.currency)}</Text>
              </View>
            ))}

            {/* Security Deposit */}
            <View className="flex-row justify-between py-3 border-t border-gray-200">
              <Text className="text-gray-700">Security deposit</Text>
              <Text className="font-medium">{formatCurrency(parseFloat(depositAmount.toFixed(2)), agreement.currency)}</Text>
            </View>

            {/* Total Due */}
            <View className="flex-row justify-between py-3 border-t border-gray-200">
              <Text className="font-bold">Total due</Text>
              <Text className="font-bold">{formatCurrency(parseFloat(totalDue.toFixed(2)), agreement.currency)}</Text>
            </View>
          </View>
        </View>

        {/* Confirm Button */}
        <TouchableOpacity
          className="bg-secondary py-4 rounded-xl items-center"
          onPress={handleConfirm}
        >
          <Text className="text-white font-semibold">Confirm Agreement</Text>
        </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}