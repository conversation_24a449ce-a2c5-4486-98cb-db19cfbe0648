import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { apiRequest } from '@/utils/apiUtils';
import { getStatusBgColor, getStatusTextColor, getCurrencySymbol } from '@/utils/statusUtils';
import { PaymentAgreement, Operator } from '@/types';

// Interface for invoice document
interface InvoiceDocument {
  document_name: string;
  document_url: string;
}

// Interface for invoices
interface Invoice {
  id: number;
  payment_agreement_id: number;
  tenant_id: number;
  user_id: string;
  total_amount: string;
  remaining_amount: number;
  status: string;
  due_date: string;
  paid_date?: string;
  plaid_transfer_id: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  lines: any[];
  payment_agreement?: PaymentAgreement;
  documents?: InvoiceDocument[];
}

export default function PaymentAgreementDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [agreement, setAgreement] = useState<PaymentAgreement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Payment Agreement - CasaPay';
    }
    fetchAgreementDetails();
  }, [id]);

  const fetchAgreementDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock payment agreement data for demo purposes
      console.log('🎭 Loading mock payment agreement details...');
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockAgreement: PaymentAgreement = {
        id: 101,
        agreement_id: "PA-2024-001",
        status: "active",
        agreement_start: "2024-01-01",
        agreement_end: "2024-12-31",
        base_monthly_rent: "2500.00",
        base_monthly_fee_amount: "150.00",
        utilities_included: false,
        payout_day: "1st of each month",
        currency: "GBP",
        tenant_id: 1,
        property_id: 1,
        entity_id: 1,
        agreement_type: "standard",
        payout_model: "monthly",
        operator_fee_amount: "25.00",
        operator_fee_percent: "1.0",
        operator_share: "1.0",
        owner_share: "99.0",
        tenant_fee_amount: "0.00",
        tenant_fee_percent: "0.0",
        tenant_plan: "standard",
        vat_pc: "20.0",
        application_link: "",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        deleted_at: null,
        addons: [],
        one_time_fees: [],
        property: {
          id: 1,
          street_address: "10 Downing Street",
          city: "London",
          state: "",
          postal_code: "SW1A 2AA",
          country: "UK",
          property_type: "apartment",
          bedrooms: 3,
          bathrooms: 2,
          size: 120,
          size_unit: "sqm"
        },
        operator: {
          id: 1,
          name: "CasaPay Management",
          entity_type: "company",
          phone: "+44 20 7946 0958",
          email: "<EMAIL>"
        }
      };
      
      setAgreement(mockAgreement);
      
      // Update document title with property address
      if (typeof document !== 'undefined') {
        document.title = `${mockAgreement.property.street_address} - CasaPay`;
      }
      
      // Fetch mock invoices
      await fetchInvoices(101);
      
      // Original API call (commented out for mock)
      /*
      const agreementResult = await apiRequest('/api/v1/tenant/mcp', 'POST', {
        jsonrpc: '2.0',
        method: 'tenant.get_payment_agreement',
        params: {
          agreement_id: parseInt(id)
        }
      });
      
      if (agreementResult.error) {
        throw new Error(agreementResult.error.message || 'Failed to fetch agreement details');
      }
      
      setAgreement(agreementResult.result.payment_agreement);
      
      // Fetch invoices for this agreement
      await fetchInvoices(parseInt(id));
      */
      
    } catch (err) {
      console.error('Error fetching agreement details:', err);
      setError('Failed to load agreement details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch invoices for the agreement
  const fetchInvoices = async (agreementId: number) => {
    try {
      // Mock invoices data for demo purposes
      console.log('🎭 Loading mock invoices...');
      
      const mockInvoices: Invoice[] = [
        {
          id: 1,
          payment_agreement_id: 101,
          tenant_id: 1,
          user_id: "user_123",
          total_amount: "2500.00",
          remaining_amount: 2500.00,
          status: "unpaid",
          due_date: "2024-02-01",
          plaid_transfer_id: null,
          notes: null,
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          lines: [
            {
              description: "Monthly Rent - February 2024",
              quantity: 1,
              unit_price: "2500.00",
              total_price: "2500.00"
            }
          ],
          documents: [
            {
              document_name: "February 2024 Invoice.pdf",
              document_url: "#"
            }
          ]
        },
        {
          id: 2,
          payment_agreement_id: 101,
          tenant_id: 1,
          user_id: "user_123",
          total_amount: "2650.00",
          remaining_amount: 0,
          status: "paid",
          due_date: "2024-01-01",
          paid_date: "2023-12-28",
          plaid_transfer_id: "transfer_abc123",
          notes: "Paid via bank transfer",
          created_at: "2023-12-01T00:00:00Z",
          updated_at: "2023-12-28T00:00:00Z",
          lines: [
            {
              description: "Monthly Rent - January 2024",
              quantity: 1,
              unit_price: "2500.00",
              total_price: "2500.00"
            },
            {
              description: "Utilities - January 2024",
              quantity: 1,
              unit_price: "150.00",
              total_price: "150.00"
            }
          ],
          documents: [
            {
              document_name: "January 2024 Invoice.pdf",
              document_url: "#"
            }
          ]
        },
        {
          id: 3,
          payment_agreement_id: 101,
          tenant_id: 1,
          user_id: "user_123",
          total_amount: "2650.00",
          remaining_amount: 0,
          status: "paid",
          due_date: "2023-12-01",
          paid_date: "2023-11-28",
          plaid_transfer_id: "transfer_def456",
          notes: "Paid via bank transfer",
          created_at: "2023-11-01T00:00:00Z",
          updated_at: "2023-11-28T00:00:00Z",
          lines: [
            {
              description: "Monthly Rent - December 2023",
              quantity: 1,
              unit_price: "2500.00",
              total_price: "2500.00"
            },
            {
              description: "Utilities - December 2023",
              quantity: 1,
              unit_price: "150.00",
              total_price: "150.00"
            }
          ],
          documents: [
            {
              document_name: "December 2023 Invoice.pdf",
              document_url: "#"
            }
          ]
        },
        {
          id: 4,
          payment_agreement_id: 101,
          tenant_id: 1,
          user_id: "user_123",
          total_amount: "2650.00",
          remaining_amount: 0,
          status: "paid",
          due_date: "2023-11-01",
          paid_date: "2023-10-29",
          plaid_transfer_id: "transfer_ghi789",
          notes: "Paid via bank transfer",
          created_at: "2023-10-01T00:00:00Z",
          updated_at: "2023-10-29T00:00:00Z",
          lines: [
            {
              description: "Monthly Rent - November 2023",
              quantity: 1,
              unit_price: "2500.00",
              total_price: "2500.00"
            },
            {
              description: "Utilities - November 2023",
              quantity: 1,
              unit_price: "150.00",
              total_price: "150.00"
            }
          ],
          documents: [
            {
              document_name: "November 2023 Invoice.pdf",
              document_url: "#"
            }
          ]
        }
      ];
      
      // Sort invoices chronologically by due date (newest first)
      const sortedInvoices = [...mockInvoices].sort((a, b) => {
        const dateA = new Date(a.due_date);
        const dateB = new Date(b.due_date);
        return dateB.getTime() - dateA.getTime();
      });
      
      setInvoices(sortedInvoices);
      
      // Original API call (commented out for mock)
      /*
      const invoicesResult = await apiRequest('/api/v1/tenant/mcp', 'POST', {
        jsonrpc: '2.0',
        method: 'tenant.query_resources',
        params: {
          resource_name: 'invoice',
          payment_agreement_id: agreementId
        }
      });
      
      if (invoicesResult.error) {
        console.error('Error fetching invoices:', invoicesResult.error);
        return;
      }
      
      if (invoicesResult.result && invoicesResult.result.data) {
        // Sort invoices chronologically by due date
        const sortedInvoices = [...invoicesResult.result.data].sort((a, b) => {
          // Simple chronological sort by due date
          const dateA = new Date(a.due_date);
          const dateB = new Date(b.due_date);
          return dateB.getTime() - dateA.getTime(); // Descending order (newest first)
        });
        setInvoices(sortedInvoices);
      }
      */
    } catch (err) {
      console.error('Error fetching invoices:', err);
    }
  };

  // Format date to YYYY-MM-DD
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
  };

  // Format date to show month and year
  const formatMonthYear = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    return `${month} ${year}`;
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 items-center justify-center">
        <ActivityIndicator size="large" color="#10B981" />
        <Text className="mt-4 text-gray-600">Loading agreement details...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50 items-center justify-center">
        <Feather name="alert-circle" size={48} color="#EF4444" />
        <Text className="mt-4 text-gray-800 font-medium">{error}</Text>
        <TouchableOpacity 
          className="mt-4 bg-primary px-4 py-2 rounded-md"
          onPress={fetchAgreementDetails}
        >
          <Text className="text-white font-medium">Try Again</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50" edges={Platform.OS === 'ios' ? ['bottom'] : ['top', 'bottom']}>
      <StatusBar style="dark" />
      
      <ScrollView className="flex-1">
        <View className="max-w-[640px] mx-auto px-4 w-full">
          {/* Main Agreement Card */}
          <View className={`bg-white rounded-lg p-6 pt-4 mb-6 border border-gray-100 ${Platform.OS === 'ios' ? 'mt-4' : 'mt-8'}`}>
            {/* Address Section */}
            <View className='mb-8'>
            <View className=" flex-row items-center">
         
              <TouchableOpacity 
                onPress={() => router.back()}
                className="mr-4"
              >
                <Text className="text-2xl">←</Text>
              </TouchableOpacity>
              <View className="flex-row items-center">
                <Text className="text-xl font-semibold">{agreement?.property.street_address}</Text>
                <View style={{ 
                  backgroundColor: getStatusBgColor(agreement?.status || ''),
                  marginLeft: 12,
                  paddingHorizontal: 12,
                  paddingVertical: 4,
                  borderRadius: 9999
                }}>
                  <Text style={{ 
                    fontSize: 12, 
                    fontWeight: '500',
                    color: getStatusTextColor(agreement?.status || '')
                  }}>
                    {agreement?.status || 'Unknown'}
                  </Text>
                </View>
            </View>
            </View>
            <Text className="text-gray-500 mt-1 text-xs">Agreement ID: {agreement?.agreement_id}</Text>
     
          </View>
            
          <View className="flex-row gap-5 mb-6">
            <View className='flex-1'>
              <Text className="text-gray-500 mb-1">Address</Text>
              <Text className="font-medium">{agreement?.property.postal_code}, {agreement?.property.city}, {agreement?.property.country}</Text>
            </View>
            {/* Monthly Rent Section */}
            <View className="flex-1">
              <Text className="text-gray-500 mb-1">Monthly Rent</Text>
              <Text className="font-medium">{getCurrencySymbol(agreement?.currency)}{agreement?.base_monthly_rent || '0'}</Text>
            </View>
            
            {/* Utilities Section */}
            <View className="flex-1">
              <Text className="text-gray-500 mb-1">Utilities</Text>
              <Text className="font-medium">{agreement?.utilities_included ? 'Included in rent' : 'Not included'}</Text>
            </View>
          </View>
            
          <View className="flex-row gap-5">
            {/* Agreement Start Section */}
            <View className="flex-1">
              <Text className="text-gray-500 mb-1">Agreement Start</Text>
              <Text className="font-medium">{formatDate(agreement?.agreement_start)}</Text>
            </View>
            
            {/* Agreement End Section */}
            <View className="flex-1">
              <Text className="text-gray-500 mb-1">Agreement End</Text>
              <Text className="font-medium">{formatDate(agreement?.agreement_end)}</Text>
            </View>

            <View className='flex-1'>
              <Text className="text-gray-500 mb-1">Payment Due Date</Text>
              <Text className="font-medium">{agreement?.payout_day}</Text>
            </View>
          </View>
            
          </View>
          
          {/* Operator Details Section */}
          <View className="bg-white rounded-lg p-6 pt-4 mb-6 border border-gray-100">
            <Text className="text-xl font-semibold mb-6">Operator Details</Text>
            
            <View className="md:flex-row flex-col gap-5 mb-4">
              {/* Operator Name */}
              <View className="flex-1">
                <Text className="text-gray-500 mb-1">Name</Text>
                <Text className="font-medium">{agreement?.operator?.name || '-'}</Text>
              </View>
              
              {/* Company */}
              <View className="flex-1">
                <Text className="text-gray-500 mb-1">Email</Text>
                <Text className="font-medium">{agreement?.operator?.email || '-'}</Text>
              </View>

              <View className="flex-1">
                <Text className="text-gray-500 mb-1">Phone</Text>
                <Text className="font-medium">{agreement?.operator?.phone || '-'}</Text>
              </View>
            </View>

          </View>

          {/* Invoices Section */}
          <View className="bg-white rounded-lg p-6 pt-4 mb-6 border border-gray-100">
            <View className="flex-row justify-between items-center mb-6">
              <Text className="text-xl font-semibold">Payment Schedule</Text>
              {invoices.some(invoice => invoice.status === 'unpaid') && (
                <TouchableOpacity 
                  className="bg-secondary rounded-lg py-2 px-4"
                  onPress={() => router.push(`/(dashboard)/payment-agreement/${id}/payment`)}
                >
                  <Text className="text-white font-medium">Make Payment</Text>
                </TouchableOpacity>
              )}
            </View>
            
            {invoices.length > 0 ? (
              invoices.map((invoice, index) => (
                <View key={invoice.id} className={`${index > 0 ? 'border-t border-gray-100 pt-4 mt-4' : ''}`}>
                  <View className="flex-row justify-between items-start mb-2">
                    <View>
                      <Text className="font-semibold">{formatMonthYear(invoice.due_date)}</Text>
                      <Text className="text-gray-500 text-xs">
                        Due on {formatDate(invoice.due_date)}
                      </Text>
                    </View>
                    
                    <View className="flex-row items-center">
                      {invoice.documents && invoice.documents.length > 0 && (
                        <TouchableOpacity 
                          className="mr-3" 
                          onPress={() => {
                            if (typeof window !== 'undefined' && invoice.documents && invoice.documents.length > 0) {
                              window.open(invoice.documents[0].document_url, '_blank');
                            }
                          }}
                        >
                          <Feather name="download" size={20} color="#000" />
                        </TouchableOpacity>
                      )}
                      
                      <View>
                        <Text className="font-semibold text-right">{getCurrencySymbol(agreement?.currency)}{invoice.total_amount}</Text>
                        <View className={`px-2 py-1 rounded-full self-end mt-1 ${invoice.status === 'paid' ? 'bg-green-100' : 'bg-yellow-100'}`}>
                          <Text className={`text-xs font-medium ${invoice.status === 'paid' ? 'text-green-800' : 'text-yellow-800'}`}>
                            {invoice.status === 'paid' ? 'Paid' : 'Pending'}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <View className="items-center py-8">
                <Text className="text-gray-500">No invoices available for this agreement</Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}