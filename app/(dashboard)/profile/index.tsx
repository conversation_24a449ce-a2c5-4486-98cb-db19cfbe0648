import React, { useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { Tenant } from '@/types';

export default function ProfileScreen() {
  const { state } = useAuth();
  // Cast the user to our extended type
  const user = (state.user as unknown) as Tenant;
  console.log(user)
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Profile - CasaPay';
    }
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-gray-50 items-center">
      <StatusBar style="dark" />
      
      <ScrollView className="flex-1 w-full">
        <View className="max-w-[640px] mx-auto px-4 w-full">
          {/* Header */}
          <View className="py-4 flex-row items-center">
            <TouchableOpacity 
              onPress={() => router.back()}
              className="mr-4"
            >
              <Text className="text-2xl">←</Text>
            </TouchableOpacity>
            <Text className="text-xl font-semibold">Account Settings</Text>
          </View>

          {/* Profile Information */}
          <View className="bg-white rounded-lg p-6 mb-6">
            <View className="flex-row items-center ">
              <View className="w-16 h-16 rounded-full bg-secondary items-center justify-center mr-4">
                <Text className="text-white text-xl font-semibold">{user?.first_name?.charAt(0) || 'U'}</Text>
              </View>
              <View>
                <Text className="text-xl font-semibold">{`${user?.first_name || ''} ${user?.last_name || ''}`}</Text>
                <Text className="text-gray-500">{user?.email || '-'}</Text>
              </View>
            </View>
          </View>

          {/* Personal Information */}
          <View className="bg-white rounded-lg p-6 mb-6">
            <Text className="text-lg font-semibold mb-4">Personal Information</Text>
            
            <View className="mb-4">
              <Text className="text-sm text-gray-500 mb-1">Full Name</Text>
              <Text className="font-medium">{`${user?.first_name || ''} ${user?.last_name || ''}`}</Text>
            </View>
            
            <View className="mb-4">
              <Text className="text-sm text-gray-500 mb-1">Email Address</Text>
              <Text className="font-medium">{user?.email || 'Not provided'}</Text>
            </View>
            
            <View className="mb-4">
              <Text className="text-sm text-gray-500 mb-1">Phone Number</Text>
              <Text className="font-medium">{user?.phone ?? 'Not provided'}</Text>
            </View>

            <View className='bg-blue-200/25 rounded-lg p-4'>
            <Text className='text-xs text-blue-900/75'>To update your account information, please contact our support team at {' '}
              <Text 
                className='text-blue-900 font-medium underline'
                onPress={() => Linking.openURL('mailto:<EMAIL>')}
              >
                <EMAIL>
              </Text>
            </Text>
            </View>
          </View>

          {/* Security Settings */}
          <View className="bg-white rounded-lg p-6 mb-6">
            <Text className="text-lg font-semibold mb-4">Security</Text>
            
            <View className="mb-4">
              <Text className="text-sm text-gray-500 mb-1">Password</Text>
              <Text className="font-medium">••••••••</Text>
            </View>
            
            <View>
              <Text className="text-sm text-gray-500 mb-1">Last Login</Text>
              <Text className="font-medium">{new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
