import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator, TouchableOpacity } from 'react-native'; // Added TouchableOpacity
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView, StyledTextInput } from '@/components/ui/StyledComponents';
import { Feather } from '@expo/vector-icons'; // Import Feather icons

export default function TenantLoginScreen() {
  const { state, login, clearError } = useAuth();
  const { message } = useLocalSearchParams<{ message: string }>();

  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Log In - CasaPay';
    }
  }, []);

  // Clear any errors when component mounts
  useEffect(() => {
    // If a success message is passed via params, display it.
    // Also, clear any existing auth error state, as the success message takes precedence.
    if (message) {
      setSuccessMessage(message);
      clearError();
    }
    // If there's no incoming success message, we don't automatically clear AuthContext.state.error on mount.
    // It will be cleared by handleLogin before a new attempt.
    // This allows an error from a failed login (that might have caused a redirect here,
    // or occurred on this screen instance) to be displayed.
  }, [message, clearError]);

  const handleLogin = async () => {
    // Clear previous errors
    clearError();
    setFormError(null);

    // Form validation
    if (!email || !password) {
      setFormError('Please enter both email and password');
      return;
    }

    // Clear success message
    setSuccessMessage(null);

    // Call login from auth context
    await login(email, password);
  };

  const handleBackToWelcome = () => {
    router.replace('/(welcome)');
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pt-10 pb-10">
        <StyledView className="w-full max-w-md mx-auto">
          {/* Back button */}
          <StyledView className="mb-6">
            <TouchableOpacity
              onPress={handleBackToWelcome}
              className="p-2 self-start"
              disabled={state.isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </TouchableOpacity>
          </StyledView>

          <StyledView className="items-center mb-12">
            <StyledText className="text-3xl font-bold text-gray-900 mb-2 text-center">Welcome back</StyledText>
            <StyledText className="text-base text-gray-600 text-center">Log in to your tenant account</StyledText>
          </StyledView>

          {/* Success message */}
          {successMessage ? (
            <StyledView className="mb-6 py-3 px-5 bg-success-accent rounded-md">
              <StyledText className="text-success text-sm">{successMessage}</StyledText>
            </StyledView>
          ) : null}

          {/* Form error message */}
          {formError ? (
            <View className="mb-6 py-3 px-5 bg-red-100 rounded-md">
              <Text className="text-red-500 text-sm">{formError}</Text>
            </View>
          ) : null}

          {/* Error message */}
          {state.error ? (
            <StyledView className="mb-6 py-3 px-5 bg-red-100 rounded-md">
              <StyledText className="text-red-500 text-sm">{state.error}</StyledText>
            </StyledView>
          ) : null}

          {/* Email Field */}
          <StyledTextInput
            label="Email address"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            editable={!state.isLoading}
            // className="mb-5" // StyledTextInput has mb-4 by default, adjust if needed
          />

          {/* Password Field */}
          <StyledTextInput
            label="Password" // Added label prop
            placeholder="Enter your password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            editable={!state.isLoading}
            // className="mb-3" // StyledTextInput has mb-4 by default
          />

          {/* Forgot Password Link - Moved and re-styled */}
          <StyledView className="flex-row justify-end items-center -mt-3 mb-4">
            {/* -mt-3 to pull it up, making it appear ~4px below the password input.
                The password input's StyledTextInput wrapper has mb-4 (16px).
                -mt-3 (12px) means 16px - 12px = 4px effective space.
                mb-4 on this StyledView maintains spacing before the Login button.
            */}
            <StyledTouchableOpacity
              onPress={() => router.push('/(auth)/forgot-password')}
              disabled={state.isLoading}
            >
              <StyledText className="text-link text-sm">Forgot password?</StyledText>
            </StyledTouchableOpacity>
          </StyledView>
          {/* Login Button */}
          <StyledTouchableOpacity
            className={`bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${state.isLoading ? 'opacity-70' : ''}`}
            onPress={handleLogin}
            disabled={state.isLoading}
          >
            {state.isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className="text-white text-base font-semibold">Log In</StyledText>
            )}
          </StyledTouchableOpacity>

          {/* Don't have an account? */}
          <StyledView className="flex-row items-center justify-center mt-8">
            {/* Increased mt from 4 to 8 for better spacing after removing the standalone forgot password link */}
            <StyledText className="text-sm text-dark-gray">Don't have an account? </StyledText>
            <StyledTouchableOpacity onPress={() => router.push('/(auth)/create-account')} disabled={state.isLoading}>
              <StyledText className="text-sm text-link font-medium">Register</StyledText>
            </StyledTouchableOpacity>
          </StyledView>

          {/* Footer spacing */}
          <StyledView className="mt-8"></StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
