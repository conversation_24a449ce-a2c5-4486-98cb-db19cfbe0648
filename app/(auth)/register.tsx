import React, { useState, useEffect } from 'react';
import { ActivityIndicator, TouchableOpacity, Linking } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTextInput, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { Feather } from '@expo/vector-icons';

type RegistrationStep = 'email' | 'pin' | 'name' | 'placeholder' | 'done';

interface FormData {
  email: string;
  pin: string;
  name: string;
}

export default function TenantRegisterScreen() {
  const { state, clearError, login } = useAuth();

  const [currentStep, setCurrentStep] = useState<RegistrationStep>('email');
  const [formData, setFormData] = useState<FormData>({
    email: '',
    pin: '',
    name: ''
  });
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);

  // Clear any errors when component mounts
  useEffect(() => {
    clearError();
  }, []);

  // Handle Terms of Use and Privacy Policy links
  const handleTermsOfUse = () => {
    // TODO: Replace with actual Terms of Use URL
    Linking.openURL('https://casapay.com/terms');
  };

  const handlePrivacyPolicy = () => {
    // TODO: Replace with actual Privacy Policy URL
    Linking.openURL('https://casapay.com/privacy');
  };

  const updateFormData = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateCurrentStep = (): boolean => {
    const errors: {[key: string]: string} = {};

    switch (currentStep) {
      case 'email':
        if (!formData.email.trim()) {
          errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          errors.email = 'Email is invalid';
        }
        break;
      case 'pin':
        if (!formData.pin.trim()) {
          errors.pin = 'Confirmation code is required';
        } else if (formData.pin.length !== 6) {
          errors.pin = 'Confirmation code must be 6 digits';
        }
        break;
      case 'name':
        if (!formData.name.trim()) {
          errors.name = 'Name is required';
        }
        break;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleContinue = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    setIsLoading(true);

    try {
      if (currentStep === 'email') {
        // TODO: Send email confirmation PIN
        // await sendEmailConfirmation(formData.email);
        setCurrentStep('pin');
      } else if (currentStep === 'pin') {
        // TODO: Verify PIN
        // await verifyEmailPin(formData.email, formData.pin);
        setCurrentStep('name');
      } else if (currentStep === 'name') {
        setCurrentStep('placeholder');
      } else if (currentStep === 'placeholder') {
        setCurrentStep('done');
      } else if (currentStep === 'done') {
        // Handle completion based on email
        if (formData.email === '<EMAIL>') {
          // For demo user, automatically log them in
          try {
            await login(formData.email, 'any-password'); // Demo login only checks email
            // login function will handle navigation to dashboard
          } catch (error) {
            // If login fails, navigate to login page with success message
            router.replace({
              pathname: '/(auth)/login',
              params: { message: `Welcome ${formData.name}! Please log in to continue.` }
            });
          }
        } else {
          // For other users, redirect to login with success message
          router.replace({
            pathname: '/(auth)/login',
            params: { message: `Welcome ${formData.name}! Please log in to continue.` }
          });
        }
      }
    } catch (error) {
      setFormErrors({
        general: error instanceof Error ? error.message : 'An error occurred. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (currentStep === 'email') {
      router.replace('/(welcome)');
    } else if (currentStep === 'pin') {
      setCurrentStep('email');
    } else if (currentStep === 'name') {
      setCurrentStep('pin');
    } else if (currentStep === 'placeholder') {
      setCurrentStep('name');
    } else if (currentStep === 'done') {
      setCurrentStep('placeholder');
    }
  };

  const getStepTitle = (): string => {
    switch (currentStep) {
      case 'email':
        return 'Enter your email address';
      case 'pin':
        return 'Check your email';
      case 'name':
        return 'What\'s your name?';
      case 'placeholder':
        return 'Almost there!';
      case 'done':
        return 'Welcome to CasaPay!';
      default:
        return '';
    }
  };

  const getStepSubtitle = (): string => {
    switch (currentStep) {
      case 'email':
        return 'We\'ll send you a confirmation code';
      case 'pin':
        return `We sent a 6-digit code to ${formData.email}`;
      case 'name':
        return 'Tell us what to call you';
      case 'placeholder':
        return 'We\'re setting up your account';
      case 'done':
        return 'Your account has been created successfully';
      default:
        return '';
    }
  };

  const getCTAText = (): string => {
    switch (currentStep) {
      case 'done':
        return 'Get Started';
      default:
        return 'Continue';
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'email':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <StyledText className="text-3xl font-bold text-gray-900 mb-2">Enter your email address</StyledText>
              <StyledText className="text-base text-gray-600">We'll send you a verification code to get started</StyledText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-sm font-medium text-gray-700 mb-2">Your email</StyledText>
              <StyledTextInput
                className={`w-full h-12 border rounded-lg px-4 py-3 bg-white transition-colors ${
                  formErrors.email
                    ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200'
                    : 'border-gray-300 focus:border-secondary focus:ring-2 focus:ring-secondary/20'
                } focus:outline-none`}
                placeholder="Enter your email address"
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={!isLoading}
              />
              {formErrors.email ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.email}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'pin':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <StyledText className="text-3xl font-bold text-gray-900 mb-2">Check your email</StyledText>
              <StyledText className="text-base text-gray-600">We sent a 6-digit code to {formData.email}</StyledText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-sm font-medium text-gray-700 mb-2">Confirmation code</StyledText>
              <StyledTextInput
                className={`w-full h-12 border rounded-lg px-4 py-3 bg-white transition-colors text-center text-lg font-mono tracking-widest ${
                  formErrors.pin
                    ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200'
                    : 'border-gray-300 focus:border-secondary focus:ring-2 focus:ring-secondary/20'
                } focus:outline-none`}
                placeholder="000000"
                value={formData.pin}
                onChangeText={(value) => updateFormData('pin', value)}
                keyboardType="number-pad"
                maxLength={6}
                editable={!isLoading}
              />
              {formErrors.pin ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.pin}</StyledText>
              ) : null}
              <StyledTouchableOpacity className="mt-4" onPress={() => setCurrentStep('email')}>
                <StyledText className="text-secondary text-sm font-medium">Didn't receive the code? Try again</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        );

      case 'name':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <StyledText className="text-3xl font-bold text-gray-900 mb-2">What's your name?</StyledText>
              <StyledText className="text-base text-gray-600">Tell us what to call you</StyledText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-sm font-medium text-gray-700 mb-2">Full name</StyledText>
              <StyledTextInput
                className={`w-full h-12 border rounded-lg px-4 py-3 bg-white transition-colors ${
                  formErrors.name
                    ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200'
                    : 'border-gray-300 focus:border-secondary focus:ring-2 focus:ring-secondary/20'
                } focus:outline-none`}
                placeholder="Enter your full name"
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
                editable={!isLoading}
              />
              {formErrors.name ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.name}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'placeholder':
        return (
          <StyledView className="w-full flex-1 justify-center">
            <StyledView className="mb-12">
              <StyledText className="text-2xl font-semibold text-black">Almost there!</StyledText>
              <StyledText className="text-sm text-gray-500 mt-2">We're setting up your account</StyledText>
            </StyledView>

            <StyledView className="items-center py-8">
              <StyledView className="w-20 h-20 bg-gray-100 rounded-full items-center justify-center mb-4">
                <Feather name="settings" size={32} color="#6B7280" />
              </StyledView>
              <StyledText className="text-sm text-gray-500 text-center mb-2">
                This step will be implemented later
              </StyledText>
              <StyledText className="text-xs text-gray-400 text-center">
                Additional account setup features coming soon
              </StyledText>
            </StyledView>
          </StyledView>
        );

      case 'done':
        return (
          <StyledView className="w-full flex-1 justify-center">
            <StyledView className="mb-12">
              <StyledText className="text-2xl font-semibold text-black">Welcome to CasaPay!</StyledText>
              <StyledText className="text-sm text-gray-500 mt-2">Your account has been created successfully</StyledText>
            </StyledView>

            <StyledView className="items-center py-8">
              <StyledView className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mb-4">
                <Feather name="check" size={32} color="#006494" />
              </StyledView>
              <StyledText className="text-sm text-gray-500 text-center mb-2">
                Account created successfully!
              </StyledText>
              <StyledText className="text-sm text-gray-500 text-center">
                Welcome to CasaPay, {formData.name}
              </StyledText>
            </StyledView>
          </StyledView>
        );

      default:
        return null;
    }
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <TouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              {currentStep === 'email' ? (
                <Feather name="x" size={24} color="#374151" />
              ) : (
                <Feather name="arrow-left" size={24} color="#374151" />
              )}
            </TouchableOpacity>
          </StyledView>

          {/* Progress indicator */}
          <StyledView className="flex-row justify-center mb-8">
            {['email', 'pin', 'name', 'placeholder', 'done'].map((step, index) => (
              <StyledView
                key={step}
                className={`w-3 h-3 rounded-full mx-1.5 transition-colors ${
                  ['email', 'pin', 'name', 'placeholder', 'done'].indexOf(currentStep) >= index
                    ? 'bg-secondary'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </StyledView>

          {/* Error message */}
          {formErrors.general ? (
            <StyledView className="mb-6 py-3 px-5 bg-red-100 rounded-md">
              <StyledText className="text-red-500 text-sm">{formErrors.general}</StyledText>
            </StyledView>
          ) : null}

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            {renderStepContent()}
          </StyledView>

          {/* Terms and Privacy for email step */}
          {currentStep === 'email' && (
            <StyledView className="mb-6">
              <StyledView className="flex-row items-center justify-center flex-wrap">
                <StyledText className="text-sm text-gray-500 text-center">
                  By registering, you accept our{' '}
                </StyledText>
                <TouchableOpacity onPress={handleTermsOfUse}>
                  <StyledText className="text-sm text-secondary underline font-medium">Terms of Use</StyledText>
                </TouchableOpacity>
                <StyledText className="text-sm text-gray-500 text-center">
                  {' '}and{' '}
                </StyledText>
                <TouchableOpacity onPress={handlePrivacyPolicy}>
                  <StyledText className="text-sm text-secondary underline font-medium">Privacy Policy</StyledText>
                </TouchableOpacity>
              </StyledView>
            </StyledView>
          )}

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`bg-secondary hover:bg-secondary/90 rounded-lg py-4 px-6 items-center w-full shadow-md transition-colors ${isLoading ? 'opacity-70' : ''}`}
            onPress={handleContinue}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className="text-white text-base font-semibold">{getCTAText()}</StyledText>
            )}
          </StyledTouchableOpacity>

          {/* Login Link for first step */}
          {currentStep === 'email' && (
            <StyledView className="flex-row items-center justify-center mt-8 mb-4">
              <StyledText className="text-sm text-gray-500">Already have an account? </StyledText>
              <StyledTouchableOpacity onPress={() => router.push('/(auth)/login')} disabled={isLoading}>
                <StyledText className="text-sm text-link font-medium">Log in</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          )}
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
