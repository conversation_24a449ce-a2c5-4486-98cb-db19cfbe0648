import React, { useState, useEffect } from 'react';
import { ActivityIndicator, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTextInput, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { Logo } from '@/components/ui/Logo';

export default function ResetPasswordScreen() {
  const { state, resetPassword, clearError } = useAuth();
  const { token: tokenFromParams } = useLocalSearchParams<{ token?: string }>();

  const [token, setToken] = useState<string | null>(null);
  const [password, setPassword] = useState('');
  const [passwordConfirmation, setPasswordConfirmation] = useState('');
  const [formError, setFormError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    clearError();
    if (tokenFromParams) {
      setToken(tokenFromParams);
    } else {
      // Handle cases where token is missing, maybe redirect or show an error
      setFormError("Password reset token is missing or invalid. Please try the 'Forgot Password' process again.");
      // Consider redirecting after a delay or providing a button to go back
    }
  }, [tokenFromParams]);

  const validateForm = () => {
    if (!password) {
      setFormError('New password is required');
      return false;
    }
    if (password.length < 8) { // Example: Basic password length validation
      setFormError('Password must be at least 8 characters long');
      return false;
    }
    if (password !== passwordConfirmation) {
      setFormError('Passwords do not match');
      return false;
    }
    setFormError(null);
    return true;
  };

  const handleResetPassword = async () => {
    if (!validateForm() || !token) {
      if (!token) setFormError("Password reset token is missing. Please try again.");
      return;
    }

    setSuccessMessage(null); // Clear previous success message
    const result = await resetPassword(token, password, passwordConfirmation);

    if (result.success) {
      setSuccessMessage(result.message || 'Password has been reset successfully! Redirecting to Log In page...');
      Alert.alert(
        'Success',
        result.message || 'Your password has been successfully reset. You can now log in.',
        [{ text: 'OK', onPress: () => router.replace('/(auth)/login') }]
      );
    } else {
      // Error is already set in auth context state.error by the resetPassword function
      // setFormError(result.message || 'Failed to reset password.'); // Or rely on state.error
    }
  };

  if (!token && !formError) {
    // Still waiting for token from params or initial check
    return (
      <StyledSafeAreaView className="flex-1 bg-white items-center justify-center">
        <ActivityIndicator size="large" color="#007AFF" />
        <StyledText className="mt-4 text-dark-gray">Loading reset information...</StyledText>
      </StyledSafeAreaView>
    );
  }

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />
      {/* Logo and Header - Moved outside ScrollView */}
      <StyledView className="items-center pt-2">
        <Logo size="large" />
        <StyledText className="text-base text-dark-gray text-center">Tenant Portal</StyledText>
      </StyledView>
      
      <StyledScrollView className="flex-grow px-6 pb-10">
        {/* Removed Logo and Header from here */}
        
        <StyledView className="items-center mb-6 mt-4">
          <StyledText className="text-2xl font-semibold mb-2 text-center text-primary">Set New Password</StyledText>
          <StyledText className="text-base text-dark-gray mb-2 text-center">
            Please enter your new password below.
          </StyledText>
        </StyledView>
          
        <StyledView className="w-full max-w-md mx-auto">
            {/* Success Message */}
            {successMessage ? (
              <StyledView className="mb-6 py-3 px-5 bg-success-accent rounded-md">
                <StyledText className="text-success text-sm">{successMessage}</StyledText>
              </StyledView>
            ) : null}

            {/* Error message from auth context */}
            {state.error && !successMessage ? ( // Only show if no success message
              <StyledView className="mb-6 py-3 px-5 bg-danger-accent rounded-md">
                <StyledText className="text-danger text-sm">{state.error}</StyledText>
              </StyledView>
            ) : null}
            
            {/* Form validation error */}
            {formError && !successMessage ? ( // Only show if no success message
              <StyledView className="mb-6 py-3 px-5 bg-danger-accent rounded-md">
                <StyledText className="text-danger text-sm">{formError}</StyledText>
              </StyledView>
            ) : null}
            
            {/* New Password Field */}
            <StyledView className="mb-6 w-full">
              <StyledText className="text-sm font-medium mb-2 text-dark-gray">New Password</StyledText>
              <StyledTextInput
                className="w-full"
                placeholder="Enter new password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                editable={!state.isLoading && !successMessage}
              />
            </StyledView>

            {/* Confirm New Password Field */}
            <StyledView className="mb-8 w-full">
              <StyledText className="text-sm font-medium mb-2 text-dark-gray">Confirm New Password</StyledText>
              <StyledTextInput
                className="w-full"
                placeholder="Confirm new password"
                value={passwordConfirmation}
                onChangeText={setPasswordConfirmation}
                secureTextEntry
                editable={!state.isLoading && !successMessage}
              />
            </StyledView>
            
            {/* Submit Button */}
            <StyledTouchableOpacity 
              className={`bg-secondary rounded-lg py-4 items-center w-full shadow-button-secondary ${(state.isLoading || successMessage) ? 'opacity-70' : ''}`}
              onPress={handleResetPassword}
              disabled={state.isLoading || !!successMessage || !token}
            >
              {state.isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <StyledText className="text-white text-base font-medium">Reset Password</StyledText>
              )}
            </StyledTouchableOpacity>
            
            {/* Back to Log In Link */}
            <StyledTouchableOpacity 
              className="items-center mt-8"
              onPress={() => router.replace('/(auth)/login')}
              disabled={state.isLoading}
            >
              <StyledText className="text-link text-sm">Back to Log In</StyledText>
            </StyledTouchableOpacity>
          </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
