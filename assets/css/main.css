@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .form-group {
    @apply mb-6;
  }

  .form-label {
    @apply mb-2 block text-left text-sm font-medium;
  }

  .form-help {
    @apply mt-1 block text-left;
  }

  .form-control {
    @apply relative block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-left min-h-[3rem] text-base;
  }

  .form-control:hover {
    @apply border-gray-200;
  }

  .form-control:focus {
    @apply border-secondary bg-white;
  }

  .form-control::placeholder {
    @apply text-sm text-gray-500/50;
  }

  .form-control--suffix {
    @apply absolute justify-center bg-light text-primary top-0 bottom-0 right-[2px] w-[3.5rem] h-[90%] flex items-center my-auto rounded-tr-xl rounded-br-xl;
  }

  .required-label {
    @apply after:content-['*'] after:ml-0.5 after:text-red-600;
  }

  .input-checkbox-label-link {
    @apply text-link underline hover:no-underline;
  }

  .invalid-feedback {
    @apply block pt-2 text-left leading-4 text-danger;
  }

  .is-invalid-control {
    @apply border-red-600/10 bg-red-100;
  }

  .is-invalid-control-large {
    @apply bg-no-repeat bg-[length:1.25rem] bg-[right_1rem_center];
  }
}

@layer base {
  html {
    @apply antialiased;
  }
}
