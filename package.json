{"name": "expo-tenant-beta", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "DARK_MODE=media DARK_MODE=media expo start --android", "ios": "DARK_MODE=media DARK_MODE=media expo start --ios", "web": "DARK_MODE=media DARK_MODE=media expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "^0.4.2", "@expo/vector-icons": "^14.1.0", "@gluestack-style/react": "^1.0.57", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.52", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.24", "@gluestack-ui/themed": "^1.1.72", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/native": "^7.0.14", "autoprefixer": "^10.4.21", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.4.7", "expo": "53.0.9", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "nativewind": "^4.1.23", "plaid": "^31.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-css-interop": "^0.1.22", "react-native-pager-view": "6.7.1", "react-native-plaid-link-sdk": "^12.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5", "tailwindcss": "^3.4.17", "ufo": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "jest": "^29.2.1", "jest-expo": "~53.0.5", "jscodeshift": "^0.15.2", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true}